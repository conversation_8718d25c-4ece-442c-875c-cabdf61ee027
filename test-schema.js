const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testProductModel() {
  try {
    console.log('🧪 Testing Product model...');
    
    // Test creating a product
    const newProduct = await prisma.product.create({
      data: {
        name: 'منتج تجريبي',
        price: 25.99,
        stock: 50
      }
    });
    
    console.log('✅ Product created successfully:');
    console.log(JSON.stringify(newProduct, null, 2));
    
    // Test getting all products
    const allProducts = await prisma.product.findMany();
    console.log(`✅ Found ${allProducts.length} products in database`);
    
  } catch (error) {
    console.error('❌ Error testing Product model:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testProductModel();
