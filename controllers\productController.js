const prisma = require('../config/prisma');

/**
 * Get all products with advanced filtering and pagination
 */
const getAllProducts = async (req, res) => {
  try {
    // Extract query parameters for filtering and pagination
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
      minPrice,
      maxPrice,
      minStock,
      maxStock
    } = req.query;

    // Validate pagination parameters
    const pageNumber = Math.max(1, parseInt(page));
    const limitNumber = Math.min(100, Math.max(1, parseInt(limit))); // Max 100 items per page
    const skip = (pageNumber - 1) * limitNumber;

    // Build where clause for filtering
    const whereClause = {};

    // Search in product name
    if (search && search.trim()) {
      whereClause.name = {
        contains: search.trim(),
        mode: 'insensitive'
      };
    }

    // Price range filtering
    if (minPrice || maxPrice) {
      whereClause.price = {};
      if (minPrice && !isNaN(parseFloat(minPrice))) {
        whereClause.price.gte = parseFloat(minPrice);
      }
      if (maxPrice && !isNaN(parseFloat(maxPrice))) {
        whereClause.price.lte = parseFloat(maxPrice);
      }
    }

    // Stock range filtering
    if (minStock || maxStock) {
      whereClause.stock = {};
      if (minStock && !isNaN(parseInt(minStock))) {
        whereClause.stock.gte = parseInt(minStock);
      }
      if (maxStock && !isNaN(parseInt(maxStock))) {
        whereClause.stock.lte = parseInt(maxStock);
      }
    }

    // Build orderBy clause
    const validSortFields = ['id', 'name', 'price', 'stock', 'createdAt'];
    const validSortOrders = ['asc', 'desc'];

    const orderByField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const orderByDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toLowerCase() : 'desc';

    // Get total count for pagination
    const totalCount = await prisma.product.count({
      where: whereClause
    });

    // Fetch products with filtering, sorting, and pagination
    const products = await prisma.product.findMany({
      where: whereClause,
      orderBy: {
        [orderByField]: orderByDirection
      },
      skip: skip,
      take: limitNumber
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limitNumber);
    const hasNextPage = pageNumber < totalPages;
    const hasPrevPage = pageNumber > 1;

    // Return response with products and metadata
    res.status(200).json({
      success: true,
      data: products,
      pagination: {
        currentPage: pageNumber,
        totalPages: totalPages,
        totalItems: totalCount,
        itemsPerPage: limitNumber,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage
      },
      filters: {
        search: search || null,
        priceRange: {
          min: minPrice ? parseFloat(minPrice) : null,
          max: maxPrice ? parseFloat(maxPrice) : null
        },
        stockRange: {
          min: minStock ? parseInt(minStock) : null,
          max: maxStock ? parseInt(maxStock) : null
        }
      },
      sorting: {
        field: orderByField,
        order: orderByDirection
      }
    });

  } catch (error) {
    console.error('Error fetching products:', error);

    // Handle specific Prisma errors
    if (error.code === 'P1001') {
      return res.status(500).json({
        success: false,
        error: 'Database connection failed',
        message: 'Unable to connect to the database. Please try again later.'
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'No products found',
        message: 'No products match the specified criteria.'
      });
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        message: 'One or more query parameters are invalid.',
        details: error.message
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while retrieving products from the database.',
      ...(process.env.NODE_ENV === 'development' && { details: error.message })
    });
  }
};

/**
 * Add a new product
 */
const addProduct = async (req, res) => {
  try {
    // Extract and validate input data
    const { name, price, stock } = req.body;

    // Input validation
    const validationErrors = [];

    // Check required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      validationErrors.push('Name is required and must be a non-empty string');
    }

    if (price === undefined || price === null) {
      validationErrors.push('Price is required');
    } else if (isNaN(price) || parseFloat(price) < 0) {
      validationErrors.push('Price must be a valid positive number');
    }

    if (stock === undefined || stock === null) {
      validationErrors.push('Stock is required');
    } else if (!Number.isInteger(Number(stock)) || parseInt(stock) < 0) {
      validationErrors.push('Stock must be a valid non-negative integer');
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid input data provided',
        details: validationErrors
      });
    }

    // Check for duplicate product name
    const existingProduct = await prisma.product.findFirst({
      where: {
        name: name.trim()
      }
    });

    if (existingProduct) {
      return res.status(400).json({
        error: 'Duplicate product',
        message: 'A product with this name already exists'
      });
    }

    // Create the new product
    const newProduct = await prisma.product.create({
      data: {
        name: name.trim(),
        price: parseFloat(price),
        stock: parseInt(stock)
      }
    });

    // Return the created product with 201 status
    res.status(201).json(newProduct);

  } catch (error) {
    console.error('Error adding product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        error: 'Duplicate entry',
        message: 'A product with this information already exists'
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        error: 'Record not found',
        message: 'The requested record was not found'
      });
    }

    // Handle database connection errors
    if (error.code === 'P1001') {
      return res.status(500).json({
        error: 'Database connection failed',
        message: 'Unable to connect to the database'
      });
    }

    // Generic server error
    res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred while adding the product'
    });
  }
};

module.exports = {
  getAllProducts,
  addProduct,
};
