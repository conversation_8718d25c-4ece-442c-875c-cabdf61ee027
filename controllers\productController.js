const prisma = require('../config/prisma');

/**
 * Get all products with advanced filtering and pagination
 */
const getAllProducts = async (req, res) => {
  try {
    // Extract query parameters for filtering and pagination
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
      minPrice,
      maxPrice,
      minStock,
      maxStock
    } = req.query;

    // Validate pagination parameters
    const pageNumber = Math.max(1, parseInt(page));
    const limitNumber = Math.min(100, Math.max(1, parseInt(limit))); // Max 100 items per page
    const skip = (pageNumber - 1) * limitNumber;

    // Build where clause for filtering
    const whereClause = {};

    // Search in product name
    if (search && search.trim()) {
      whereClause.name = {
        contains: search.trim(),
        mode: 'insensitive'
      };
    }

    // Price range filtering
    if (minPrice || maxPrice) {
      whereClause.price = {};
      if (minPrice && !isNaN(parseFloat(minPrice))) {
        whereClause.price.gte = parseFloat(minPrice);
      }
      if (maxPrice && !isNaN(parseFloat(maxPrice))) {
        whereClause.price.lte = parseFloat(maxPrice);
      }
    }

    // Stock range filtering
    if (minStock || maxStock) {
      whereClause.stock = {};
      if (minStock && !isNaN(parseInt(minStock))) {
        whereClause.stock.gte = parseInt(minStock);
      }
      if (maxStock && !isNaN(parseInt(maxStock))) {
        whereClause.stock.lte = parseInt(maxStock);
      }
    }

    // Build orderBy clause
    const validSortFields = ['id', 'name', 'price', 'stock', 'createdAt'];
    const validSortOrders = ['asc', 'desc'];

    const orderByField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
    const orderByDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toLowerCase() : 'desc';

    // Get total count for pagination
    const totalCount = await prisma.product.count({
      where: whereClause
    });

    // Fetch products with filtering, sorting, and pagination
    const products = await prisma.product.findMany({
      where: whereClause,
      orderBy: {
        [orderByField]: orderByDirection
      },
      skip: skip,
      take: limitNumber
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limitNumber);
    const hasNextPage = pageNumber < totalPages;
    const hasPrevPage = pageNumber > 1;

    // Return response with products and metadata
    res.status(200).json({
      success: true,
      data: products,
      pagination: {
        currentPage: pageNumber,
        totalPages: totalPages,
        totalItems: totalCount,
        itemsPerPage: limitNumber,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage
      },
      filters: {
        search: search || null,
        priceRange: {
          min: minPrice ? parseFloat(minPrice) : null,
          max: maxPrice ? parseFloat(maxPrice) : null
        },
        stockRange: {
          min: minStock ? parseInt(minStock) : null,
          max: maxStock ? parseInt(maxStock) : null
        }
      },
      sorting: {
        field: orderByField,
        order: orderByDirection
      }
    });

  } catch (error) {
    console.error('Error fetching products:', error);

    // Handle specific Prisma errors
    if (error.code === 'P1001') {
      return res.status(500).json({
        success: false,
        error: 'Database connection failed',
        message: 'Unable to connect to the database. Please try again later.'
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'No products found',
        message: 'No products match the specified criteria.'
      });
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        message: 'One or more query parameters are invalid.',
        details: error.message
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while retrieving products from the database.',
      ...(process.env.NODE_ENV === 'development' && { details: error.message })
    });
  }
};

/**
 * Add a new product to the database
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with created product or error
 */
const addProduct = async (req, res) => {
  try {
    console.log('📝 Adding new product...', req.body);

    // Extract and validate input data
    const { name, price, stock } = req.body;

    // Input validation with detailed error messages
    const validationErrors = [];

    // Validate name field
    if (!name) {
      validationErrors.push({
        field: 'name',
        message: 'Name is required',
        code: 'REQUIRED_FIELD'
      });
    } else if (typeof name !== 'string') {
      validationErrors.push({
        field: 'name',
        message: 'Name must be a string',
        code: 'INVALID_TYPE'
      });
    } else if (name.trim().length === 0) {
      validationErrors.push({
        field: 'name',
        message: 'Name cannot be empty or contain only spaces',
        code: 'EMPTY_VALUE'
      });
    } else if (name.trim().length > 255) {
      validationErrors.push({
        field: 'name',
        message: 'Name cannot exceed 255 characters',
        code: 'MAX_LENGTH_EXCEEDED'
      });
    }

    // Validate price field
    if (price === undefined || price === null) {
      validationErrors.push({
        field: 'price',
        message: 'Price is required',
        code: 'REQUIRED_FIELD'
      });
    } else if (isNaN(price)) {
      validationErrors.push({
        field: 'price',
        message: 'Price must be a valid number',
        code: 'INVALID_TYPE'
      });
    } else if (parseFloat(price) < 0) {
      validationErrors.push({
        field: 'price',
        message: 'Price must be a positive number',
        code: 'NEGATIVE_VALUE'
      });
    } else if (parseFloat(price) > 999999.99) {
      validationErrors.push({
        field: 'price',
        message: 'Price cannot exceed 999,999.99',
        code: 'MAX_VALUE_EXCEEDED'
      });
    }

    // Validate stock field
    if (stock === undefined || stock === null) {
      validationErrors.push({
        field: 'stock',
        message: 'Stock is required',
        code: 'REQUIRED_FIELD'
      });
    } else if (!Number.isInteger(Number(stock))) {
      validationErrors.push({
        field: 'stock',
        message: 'Stock must be a valid integer',
        code: 'INVALID_TYPE'
      });
    } else if (parseInt(stock) < 0) {
      validationErrors.push({
        field: 'stock',
        message: 'Stock must be a non-negative integer',
        code: 'NEGATIVE_VALUE'
      });
    } else if (parseInt(stock) > 999999) {
      validationErrors.push({
        field: 'stock',
        message: 'Stock cannot exceed 999,999',
        code: 'MAX_VALUE_EXCEEDED'
      });
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      console.log('❌ Validation failed:', validationErrors);
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        message: 'The provided data contains validation errors',
        details: validationErrors,
        timestamp: new Date().toISOString()
      });
    }

    // Sanitize input data
    const sanitizedName = name.trim();
    const sanitizedPrice = parseFloat(price);
    const sanitizedStock = parseInt(stock);

    // Check for duplicate product name (case-insensitive)
    const existingProduct = await prisma.product.findFirst({
      where: {
        name: {
          equals: sanitizedName,
          mode: 'insensitive'
        }
      }
    });

    if (existingProduct) {
      console.log('❌ Duplicate product name:', sanitizedName);
      return res.status(409).json({
        success: false,
        error: 'Duplicate product',
        message: 'A product with this name already exists',
        conflictingProduct: {
          id: existingProduct.id,
          name: existingProduct.name
        },
        timestamp: new Date().toISOString()
      });
    }

    // Create the new product
    console.log('✅ Creating product with data:', {
      name: sanitizedName,
      price: sanitizedPrice,
      stock: sanitizedStock
    });

    const newProduct = await prisma.product.create({
      data: {
        name: sanitizedName,
        price: sanitizedPrice,
        stock: sanitizedStock
      }
    });

    console.log('🎉 Product created successfully:', newProduct);

    // Return the created product with success response
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: newProduct,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error adding product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(409).json({
        success: false,
        error: 'Duplicate entry',
        message: 'A product with this information already exists',
        details: {
          constraint: error.meta?.target || 'unknown',
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'Record not found',
        message: 'The requested record was not found',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database connection errors
    if (error.code === 'P1001') {
      return res.status(503).json({
        success: false,
        error: 'Database connection failed',
        message: 'Unable to connect to the database. Please try again later.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database timeout errors
    if (error.code === 'P1008') {
      return res.status(408).json({
        success: false,
        error: 'Database timeout',
        message: 'Database operation timed out. Please try again.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle JSON parsing errors
    if (error instanceof SyntaxError && error.message.includes('JSON')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid JSON',
        message: 'The request body contains invalid JSON',
        timestamp: new Date().toISOString()
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while adding the product',
      ...(process.env.NODE_ENV === 'development' && {
        details: {
          message: error.message,
          stack: error.stack
        }
      }),
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Update an existing product by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with updated product or error
 */
const updateProduct = async (req, res) => {
  try {
    console.log('📝 Updating product...', { id: req.params.id, body: req.body });

    // Extract product ID from URL parameters
    const { id } = req.params;

    // Extract update data from request body
    const { name, price, stock } = req.body;

    // Validate product ID
    if (!id || isNaN(parseInt(id))) {
      console.log('❌ Invalid product ID:', id);
      return res.status(400).json({
        success: false,
        error: 'Invalid product ID',
        message: 'Product ID must be a valid positive integer',
        timestamp: new Date().toISOString()
      });
    }

    const productId = parseInt(id);

    // Check if at least one field is provided for update
    if (!name && price === undefined && stock === undefined) {
      console.log('❌ No update data provided');
      return res.status(400).json({
        success: false,
        error: 'No update data provided',
        message: 'At least one field (name, price, or stock) must be provided for update',
        timestamp: new Date().toISOString()
      });
    }

    // Input validation for provided fields
    const validationErrors = [];
    const updateData = {};

    // Validate name field if provided
    if (name !== undefined) {
      if (typeof name !== 'string') {
        validationErrors.push({
          field: 'name',
          message: 'Name must be a string',
          code: 'INVALID_TYPE'
        });
      } else if (name.trim().length === 0) {
        validationErrors.push({
          field: 'name',
          message: 'Name cannot be empty or contain only spaces',
          code: 'EMPTY_VALUE'
        });
      } else if (name.trim().length > 255) {
        validationErrors.push({
          field: 'name',
          message: 'Name cannot exceed 255 characters',
          code: 'MAX_LENGTH_EXCEEDED'
        });
      } else {
        updateData.name = name.trim();
      }
    }

    // Validate price field if provided
    if (price !== undefined) {
      if (isNaN(price)) {
        validationErrors.push({
          field: 'price',
          message: 'Price must be a valid number',
          code: 'INVALID_TYPE'
        });
      } else if (parseFloat(price) < 0) {
        validationErrors.push({
          field: 'price',
          message: 'Price must be a positive number',
          code: 'NEGATIVE_VALUE'
        });
      } else if (parseFloat(price) > 999999.99) {
        validationErrors.push({
          field: 'price',
          message: 'Price cannot exceed 999,999.99',
          code: 'MAX_VALUE_EXCEEDED'
        });
      } else {
        updateData.price = parseFloat(price);
      }
    }

    // Validate stock field if provided
    if (stock !== undefined) {
      if (!Number.isInteger(Number(stock))) {
        validationErrors.push({
          field: 'stock',
          message: 'Stock must be a valid integer',
          code: 'INVALID_TYPE'
        });
      } else if (parseInt(stock) < 0) {
        validationErrors.push({
          field: 'stock',
          message: 'Stock must be a non-negative integer',
          code: 'NEGATIVE_VALUE'
        });
      } else if (parseInt(stock) > 999999) {
        validationErrors.push({
          field: 'stock',
          message: 'Stock cannot exceed 999,999',
          code: 'MAX_VALUE_EXCEEDED'
        });
      } else {
        updateData.stock = parseInt(stock);
      }
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      console.log('❌ Validation failed:', validationErrors);
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        message: 'The provided data contains validation errors',
        details: validationErrors,
        timestamp: new Date().toISOString()
      });
    }

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!existingProduct) {
      console.log('❌ Product not found:', productId);
      return res.status(404).json({
        success: false,
        error: 'Product not found',
        message: `Product with ID ${productId} does not exist`,
        timestamp: new Date().toISOString()
      });
    }

    // Check for duplicate name if name is being updated
    if (updateData.name && updateData.name !== existingProduct.name) {
      const duplicateProduct = await prisma.product.findFirst({
        where: {
          name: {
            equals: updateData.name,
            mode: 'insensitive'
          },
          id: {
            not: productId
          }
        }
      });

      if (duplicateProduct) {
        console.log('❌ Duplicate product name:', updateData.name);
        return res.status(409).json({
          success: false,
          error: 'Duplicate product name',
          message: 'A product with this name already exists',
          conflictingProduct: {
            id: duplicateProduct.id,
            name: duplicateProduct.name
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    console.log('✅ Updating product with data:', updateData);

    // Update the product
    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: updateData
    });

    console.log('🎉 Product updated successfully:', updatedProduct);

    // Return the updated product
    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      data: updatedProduct,
      updatedFields: Object.keys(updateData),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error updating product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(409).json({
        success: false,
        error: 'Duplicate entry',
        message: 'A product with this information already exists',
        details: {
          constraint: error.meta?.target || 'unknown',
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
        message: 'The product you are trying to update does not exist',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database connection errors
    if (error.code === 'P1001') {
      return res.status(503).json({
        success: false,
        error: 'Database connection failed',
        message: 'Unable to connect to the database. Please try again later.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database timeout errors
    if (error.code === 'P1008') {
      return res.status(408).json({
        success: false,
        error: 'Database timeout',
        message: 'Database operation timed out. Please try again.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while updating the product',
      ...(process.env.NODE_ENV === 'development' && {
        details: {
          message: error.message,
          stack: error.stack
        }
      }),
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Delete a product by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response with deletion confirmation or error
 */
const deleteProduct = async (req, res) => {
  try {
    console.log('🗑️ Deleting product...', { id: req.params.id });

    // Extract product ID from URL parameters
    const { id } = req.params;

    // Validate product ID
    if (!id || isNaN(parseInt(id))) {
      console.log('❌ Invalid product ID:', id);
      return res.status(400).json({
        success: false,
        error: 'Invalid product ID',
        message: 'Product ID must be a valid positive integer',
        timestamp: new Date().toISOString()
      });
    }

    const productId = parseInt(id);

    // Check if product ID is positive
    if (productId <= 0) {
      console.log('❌ Product ID must be positive:', productId);
      return res.status(400).json({
        success: false,
        error: 'Invalid product ID',
        message: 'Product ID must be a positive integer',
        timestamp: new Date().toISOString()
      });
    }

    // Check if product exists before deletion
    const existingProduct = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!existingProduct) {
      console.log('❌ Product not found:', productId);
      return res.status(404).json({
        success: false,
        error: 'Product not found',
        message: `Product with ID ${productId} does not exist`,
        timestamp: new Date().toISOString()
      });
    }

    console.log('✅ Product found, proceeding with deletion:', {
      id: existingProduct.id,
      name: existingProduct.name
    });

    // Delete the product
    const deletedProduct = await prisma.product.delete({
      where: { id: productId }
    });

    console.log('🎉 Product deleted successfully:', deletedProduct);

    // Return success confirmation
    res.status(200).json({
      success: true,
      message: 'Product deleted successfully',
      deletedProduct: {
        id: deletedProduct.id,
        name: deletedProduct.name,
        price: deletedProduct.price,
        stock: deletedProduct.stock,
        createdAt: deletedProduct.createdAt
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error deleting product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'Product not found',
        message: 'The product you are trying to delete does not exist',
        details: {
          prismaCode: error.code,
          cause: error.meta?.cause || 'Record to delete does not exist'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle foreign key constraint errors (if product is referenced elsewhere)
    if (error.code === 'P2003') {
      return res.status(409).json({
        success: false,
        error: 'Cannot delete product',
        message: 'This product cannot be deleted because it is referenced by other records',
        details: {
          prismaCode: error.code,
          constraint: error.meta?.field_name || 'unknown'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database connection errors
    if (error.code === 'P1001') {
      return res.status(503).json({
        success: false,
        error: 'Database connection failed',
        message: 'Unable to connect to the database. Please try again later.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle database timeout errors
    if (error.code === 'P1008') {
      return res.status(408).json({
        success: false,
        error: 'Database timeout',
        message: 'Database operation timed out. Please try again.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Handle transaction errors
    if (error.code === 'P2034') {
      return res.status(409).json({
        success: false,
        error: 'Transaction conflict',
        message: 'The operation conflicts with another transaction. Please try again.',
        details: {
          prismaCode: error.code
        },
        timestamp: new Date().toISOString()
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'An unexpected error occurred while deleting the product',
      ...(process.env.NODE_ENV === 'development' && {
        details: {
          message: error.message,
          stack: error.stack
        }
      }),
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  getAllProducts,
  addProduct,
  updateProduct,
  deleteProduct,
};
