const prisma = require('../config/prisma');

/**
 * Get all products
 */
const getAllProducts = async (req, res) => {
  try {
    const products = await prisma.product.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    res.status(200).json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      error: 'Failed to fetch products',
      message: 'An error occurred while retrieving products from the database'
    });
  }
};

/**
 * Add a new product
 */
const addProduct = async (req, res) => {
  try {
    // Extract and validate input data
    const { name, price, stock } = req.body;

    // Input validation
    const validationErrors = [];

    // Check required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      validationErrors.push('Name is required and must be a non-empty string');
    }

    if (price === undefined || price === null) {
      validationErrors.push('Price is required');
    } else if (isNaN(price) || parseFloat(price) < 0) {
      validationErrors.push('Price must be a valid positive number');
    }

    if (stock === undefined || stock === null) {
      validationErrors.push('Stock is required');
    } else if (!Number.isInteger(Number(stock)) || parseInt(stock) < 0) {
      validationErrors.push('Stock must be a valid non-negative integer');
    }

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid input data provided',
        details: validationErrors
      });
    }

    // Check for duplicate product name
    const existingProduct = await prisma.product.findFirst({
      where: {
        name: name.trim()
      }
    });

    if (existingProduct) {
      return res.status(400).json({
        error: 'Duplicate product',
        message: 'A product with this name already exists'
      });
    }

    // Create the new product
    const newProduct = await prisma.product.create({
      data: {
        name: name.trim(),
        price: parseFloat(price),
        stock: parseInt(stock)
      }
    });

    // Return the created product with 201 status
    res.status(201).json(newProduct);

  } catch (error) {
    console.error('Error adding product:', error);

    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        error: 'Duplicate entry',
        message: 'A product with this information already exists'
      });
    }

    if (error.code === 'P2025') {
      return res.status(404).json({
        error: 'Record not found',
        message: 'The requested record was not found'
      });
    }

    // Handle database connection errors
    if (error.code === 'P1001') {
      return res.status(500).json({
        error: 'Database connection failed',
        message: 'Unable to connect to the database'
      });
    }

    // Generic server error
    res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred while adding the product'
    });
  }
};

module.exports = {
  getAllProducts,
  addProduct,
};
