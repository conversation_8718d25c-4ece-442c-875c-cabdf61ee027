const express = require('express');
const router = express.Router();
const { getAllProducts, addProduct, updateProduct, deleteProduct } = require('../controllers/productController');

// GET /products - Get all products
router.get('/products', getAllProducts);

// POST /products - Add a new product
router.post('/products', addProduct);

// PUT /products/:id - Update a product by ID
router.put('/products/:id', updateProduct);

// DELETE /products/:id - Delete a product by ID
router.delete('/products/:id', deleteProduct);

module.exports = router;
