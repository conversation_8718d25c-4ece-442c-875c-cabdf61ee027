{"name": "cashier-system", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "prisma": "^6.9.0"}, "devDependencies": {"nodemon": "^3.1.10"}}