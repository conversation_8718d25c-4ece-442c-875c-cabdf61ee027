const http = require('http');

function testAPI() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/products',
    method: 'GET'
  };

  console.log('🧪 Testing GET /api/products...');

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      console.log('Status Code:', res.statusCode);
      try {
        const result = JSON.parse(data);
        console.log('✅ Success! API is working');
        console.log('Response structure:', {
          success: result.success,
          dataCount: result.data ? result.data.length : 0,
          hasPagination: !!result.pagination
        });
      } catch (error) {
        console.log('❌ JSON Parse Error:', error.message);
        console.log('Raw response:', data.substring(0, 200));
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ Request Error:', error.message);
  });

  req.end();
}

testAPI();
