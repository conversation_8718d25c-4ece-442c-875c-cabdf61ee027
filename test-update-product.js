const http = require('http');

/**
 * Function to make PUT request to update product
 */
function updateProduct(productId, updateData, testDescription) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(updateData);

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/products/${productId}`,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n🧪 ${testDescription}`);
    console.log(`📡 PUT /api/products/${productId}`);
    console.log(`📦 Data:`, updateData);

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`✅ Status: ${res.statusCode}`);
          console.log(`📄 Response:`, JSON.stringify(response, null, 2));
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          console.log(`❌ Invalid JSON response:`, data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Request failed:`, error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * Function to make POST request to create a test product
 */
function createTestProduct(productData) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(productData);

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/products',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

/**
 * Test different scenarios for updating products
 */
async function testUpdateProductAPI() {
  console.log('🚀 Testing PUT /api/products/:id endpoint...\n');

  try {
    // First, create a test product
    console.log('📝 Creating test product...');
    const testProduct = await createTestProduct({
      name: 'منتج للتعديل',
      price: 50.00,
      stock: 25
    });

    if (!testProduct.success) {
      throw new Error('Failed to create test product');
    }

    const productId = testProduct.data.id;
    console.log(`✅ Test product created with ID: ${productId}\n`);

    // Test 1: Update all fields
    await updateProduct(productId, {
      name: 'منتج محدث',
      price: 75.50,
      stock: 40
    }, 'Test 1: Update all fields');

    // Test 2: Update only name
    await updateProduct(productId, {
      name: 'منتج محدث مرة أخرى'
    }, 'Test 2: Update only name');

    // Test 3: Update only price
    await updateProduct(productId, {
      price: 99.99
    }, 'Test 3: Update only price');

    // Test 4: Update only stock
    await updateProduct(productId, {
      stock: 100
    }, 'Test 4: Update only stock');

    // Test 5: Invalid product ID
    await updateProduct('invalid_id', {
      name: 'منتج جديد'
    }, 'Test 5: Invalid product ID');

    // Test 6: Non-existent product ID
    await updateProduct(99999, {
      name: 'منتج غير موجود'
    }, 'Test 6: Non-existent product ID');

    // Test 7: No update data provided
    await updateProduct(productId, {}, 'Test 7: No update data provided');

    // Test 8: Invalid data types
    await updateProduct(productId, {
      name: '',
      price: 'invalid_price',
      stock: -10
    }, 'Test 8: Invalid data types and values');

    // Test 9: Create another product for duplicate name test
    const anotherProduct = await createTestProduct({
      name: 'منتج آخر',
      price: 30.00,
      stock: 15
    });

    if (anotherProduct.success) {
      // Test 10: Duplicate name
      await updateProduct(productId, {
        name: 'منتج آخر'
      }, 'Test 10: Duplicate product name');
    }

    console.log('\n✅ All tests completed!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

// Run tests
testUpdateProductAPI();
